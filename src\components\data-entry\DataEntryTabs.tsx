
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import DataEntryView from "./DataEntryView";
import PerformanceHistoryView from "./PerformanceHistoryView";
import { useDashboard } from "@/contexts/DashboardContext";

const DataEntryTabs = () => {
  const { selectedProject } = useDashboard();

  return (
    <div className="space-y-6">
      <Tabs defaultValue="entry" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="entry">Daily Logs</TabsTrigger>
          <TabsTrigger value="history">Performance History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="entry" className="space-y-6">
          <DataEntryView />
        </TabsContent>
        
        <TabsContent value="history" className="space-y-6">
          <PerformanceHistoryView projectId={selectedProject} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataEntryTabs;
