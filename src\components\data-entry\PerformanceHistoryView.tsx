
import React, { useState } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { format } from "date-fns";
import { Edit, Trash2, Save, X, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { TimeInput } from "@/components/ui/time-input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import UniversalDropdown, { DropdownOption } from "@/components/ui/universal-dropdown";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { AgentPerformance } from "@/hooks/dashboard/useAgentPerformanceOperations";

interface PerformanceHistoryViewProps {
  agentId?: string;
  projectId?: string;
}

const PerformanceHistoryView: React.FC<PerformanceHistoryViewProps> = ({
  agentId,
  projectId,
}) => {
  const {
    performanceData,
    updatePerformanceData,
    deletePerformanceData,
    formatTalkTime,
    parseTalkTime,
    getProjectById,
    getAgentById,
    projects,
    agents
  } = useDashboard();
  const { toast } = useToast();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editData, setEditData] = useState<Partial<AgentPerformance>>({});
  const [selectedFopProjects, setSelectedFopProjects] = useState<string[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);


  // Generate week options (Week 1-5)
  const weekOptions = Array.from({ length: 5 }, (_, i) => `Week ${i + 1}`);

  // Convert to dropdown options
  const weekDropdownOptions: DropdownOption[] = weekOptions.map(week => ({
    value: week,
    label: week
  }));

  const projectDropdownOptions: DropdownOption[] = projects
    .filter(project => project.id !== "all")
    .map(project => ({
      value: project.id,
      label: project.name
    }));

  const agentDropdownOptions: DropdownOption[] = agents.map(agent => ({
    value: agent.id,
    label: agent.name
  }));

  const fopProjectDropdownOptions: DropdownOption[] = projects
    .filter(project => !selectedFopProjects.includes(project.id))
    .map(project => ({
      value: project.id,
      label: project.name
    }));

  // Simple filtering with prop-based filters only
  const filteredData = performanceData.filter(item => {
    // Apply prop-based filters
    if (agentId && item.agent_id !== agentId) return false;
    if (projectId && item.project_id !== projectId) return false;

    return true;
  }).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); // Sort by date descending

  const startEdit = (item: AgentPerformance) => {
    setEditingId(item.id);
    setEditData({
      date: item.date,
      week: item.week,
      agent_id: item.agent_id,
      project_id: item.project_id,
      dials: item.dials,
      connected: item.connected,
      talk_time: item.talk_time,
      scheduled_meetings: item.scheduled_meetings,
      successful_meetings: item.successful_meetings,
      fop_scheduled: item.fop_scheduled,
      fop_successful: item.fop_successful,
      fop_projects: item.fop_projects || [],
    });
    setSelectedFopProjects(item.fop_projects || []);
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditData({});
    setSelectedFopProjects([]);
  };

  const saveEdit = async (id: string) => {
    if (isUpdating) return;
    
    setIsUpdating(true);
    console.log('Saving edit for ID:', id);
    console.log('Edit data:', editData);
    
    try {
      // Prepare the update data with proper structure
      const updateData = {
        ...editData,
        fop_projects: selectedFopProjects,
        updated_at: new Date().toISOString()
      };
      
      console.log('Prepared update data:', updateData);
      
      const success = await updatePerformanceData(id, updateData);
      
      if (success) {
        console.log('✅ Update successful, clearing edit state');

        // Clear editing state immediately for instant UI feedback
        setEditingId(null);
        setEditData({});
        setSelectedFopProjects([]);

        // Show success message
        toast({
          title: "✅ Updated Successfully",
          description: "Changes synced in real-time across all tabs and users.",
          variant: "default"
        });

      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      console.error('Error updating performance data:', error);
      toast({
        title: "Update Failed",
        description: `Failed to update performance data: ${error.message || 'Unknown error'}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const deleteEntry = async (id: string) => {
    try {
      console.log('🗑️ Initiating delete for performance data ID:', id);

      // Show immediate loading state
      toast({
        title: "Deleting...",
        description: "Removing performance entry...",
        variant: "default"
      });

      const success = await deletePerformanceData(id);

      if (success) {
        console.log('✅ Delete successful, entry should be removed from UI');

        // Immediate success feedback
        toast({
          title: "✅ Deleted Successfully",
          description: "Changes synced in real-time across all tabs and users.",
          variant: "default"
        });

        // Force component re-render by triggering a state update
        // This ensures the UI reflects the change immediately
        setEditingId(null);

      } else {
        throw new Error('Delete operation returned false');
      }
    } catch (error) {
      console.error('❌ Error deleting performance data:', error);
      toast({
        title: "❌ Delete Failed",
        description: "Failed to delete entry. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatTalkTimeForEdit = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Performance History
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                View and edit past performance entries ({filteredData.length} entries)
              </p>
            </div>
          </div>
        </div>

        {/* Main Table Container */}
        <div className="relative">
          <div className="overflow-x-auto overflow-y-visible">
            <Table className="min-w-[1200px]">
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[120px]">Date</TableHead>
                <TableHead className="min-w-[100px]">Week</TableHead>
                <TableHead className="min-w-[120px]">Agent</TableHead>
                <TableHead className="min-w-[120px]">Project</TableHead>
                <TableHead className="min-w-[80px]">Dials</TableHead>
                <TableHead className="min-w-[90px]">Connected</TableHead>
                <TableHead className="min-w-[100px]">Talk Time</TableHead>
                <TableHead className="min-w-[90px]">Scheduled</TableHead>
                <TableHead className="min-w-[90px]">Successful</TableHead>
                <TableHead className="min-w-[110px]">FOP Scheduled</TableHead>
                <TableHead className="min-w-[110px]">FOP Successful</TableHead>
                <TableHead className="min-w-[120px]">FOP Projects</TableHead>
                <TableHead className="min-w-[120px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50 transition-colors duration-200">
                  {/* Date Field - Editable */}
                  <TableCell>
                    {editingId === item.id ? (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !editData.date && "text-muted-foreground"
                            )}
                          >
                            <Calendar className="mr-2 h-4 w-4" />
                            {editData.date ? format(new Date(editData.date), "MMM dd, yyyy") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-auto p-0 bg-white border shadow-lg z-[9999] animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
                          side="bottom"
                          align="start"
                          sideOffset={5}
                        >
                          <CalendarComponent
                            mode="single"
                            selected={editData.date ? new Date(editData.date) : undefined}
                            onSelect={(date) => setEditData({...editData, date: date?.toISOString().split('T')[0] || ''})}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    ) : (
                      format(new Date(item.date), "MMM dd, yyyy")
                    )}
                  </TableCell>

                  {/* Week Field - Editable */}
                  <TableCell>
                    {editingId === item.id ? (
                      <UniversalDropdown
                        options={weekDropdownOptions}
                        placeholder="Select week"
                        value={weekDropdownOptions.find(option => option.value === editData.week) || null}
                        onChange={(option) => setEditData({...editData, week: option.value})}
                        className="w-full"
                        minWidth="150px"
                      />
                    ) : (
                      item.week || "-"
                    )}
                  </TableCell>

                  {/* Agent Field - Editable */}
                  <TableCell>
                    {editingId === item.id ? (
                      <UniversalDropdown
                        options={agentDropdownOptions}
                        placeholder="Select agent"
                        value={agentDropdownOptions.find(option => option.value === editData.agent_id) || null}
                        onChange={(option) => setEditData({...editData, agent_id: option.value})}
                        className="w-full"
                        minWidth="180px"
                      />
                    ) : (
                      getAgentById(item.agent_id)?.name || "Unknown"
                    )}
                  </TableCell>

                  {/* Project Field - Editable */}
                  <TableCell>
                    {editingId === item.id ? (
                      <UniversalDropdown
                        options={projectDropdownOptions}
                        placeholder="Select project"
                        value={projectDropdownOptions.find(option => option.value === editData.project_id) || null}
                        onChange={(option) => setEditData({...editData, project_id: option.value})}
                        className="w-full"
                        minWidth="180px"
                      />
                    ) : (
                      getProjectById(item.project_id)?.name || "Unknown"
                    )}
                  </TableCell>
                  
                  {/* Editable fields */}
                  <TableCell>
                    {editingId === item.id ? (
                      <Input
                        type="number"
                        min="0"
                        value={editData.dials || 0}
                        onChange={(e) => setEditData({...editData, dials: parseInt(e.target.value) || 0})}
                        className="w-20"
                      />
                    ) : (
                      item.dials
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {editingId === item.id ? (
                      <Input
                        type="number"
                        min="0"
                        value={editData.connected || 0}
                        onChange={(e) => setEditData({...editData, connected: parseInt(e.target.value) || 0})}
                        className="w-20"
                      />
                    ) : (
                      item.connected
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {editingId === item.id ? (
                      <TimeInput
                        value={formatTalkTimeForEdit(editData.talk_time || 0)}
                        onChange={(value) => setEditData({...editData, talk_time: parseTalkTime(value)})}
                        className="w-24"
                      />
                    ) : (
                      formatTalkTime(item.talk_time)
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {editingId === item.id ? (
                      <Input
                        type="number"
                        min="0"
                        value={editData.scheduled_meetings || 0}
                        onChange={(e) => setEditData({...editData, scheduled_meetings: parseInt(e.target.value) || 0})}
                        className="w-20"
                      />
                    ) : (
                      item.scheduled_meetings
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {editingId === item.id ? (
                      <Input
                        type="number"
                        min="0"
                        value={editData.successful_meetings || 0}
                        onChange={(e) => setEditData({...editData, successful_meetings: parseInt(e.target.value) || 0})}
                        className="w-20"
                      />
                    ) : (
                      item.successful_meetings
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {editingId === item.id ? (
                      <Input
                        type="number"
                        min="0"
                        value={editData.fop_scheduled || 0}
                        onChange={(e) => setEditData({...editData, fop_scheduled: parseInt(e.target.value) || 0})}
                        className="w-20"
                      />
                    ) : (
                      item.fop_scheduled
                    )}
                  </TableCell>
                  
                  <TableCell>
                    {editingId === item.id ? (
                      <Input
                        type="number"
                        min="0"
                        value={editData.fop_successful || 0}
                        onChange={(e) => setEditData({...editData, fop_successful: parseInt(e.target.value) || 0})}
                        className="w-20"
                      />
                    ) : (
                      item.fop_successful
                    )}
                  </TableCell>

                  {/* FOP Projects Field */}
                  <TableCell>
                    {editingId === item.id ? (
                      <div className="space-y-2">
                        <UniversalDropdown
                          options={fopProjectDropdownOptions}
                          placeholder="Add FOP project"
                          value={null}
                          onChange={(option) => {
                            if (!selectedFopProjects.includes(option.value)) {
                              const newProjects = [...selectedFopProjects, option.value];
                              setSelectedFopProjects(newProjects);
                              setEditData({...editData, fop_projects: newProjects});
                            }
                          }}
                          className="w-full"
                          minWidth="200px"
                        />
                        <div className="flex flex-wrap gap-1">
                          {selectedFopProjects.map((projectId) => {
                            const project = getProjectById(projectId);
                            return (
                              <div
                                key={projectId}
                                className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs"
                              >
                                {project?.name || 'Unknown'}
                                <button
                                  onClick={() => {
                                    const newProjects = selectedFopProjects.filter(id => id !== projectId);
                                    setSelectedFopProjects(newProjects);
                                    setEditData({...editData, fop_projects: newProjects});
                                  }}
                                  className="ml-1 text-green-600 hover:text-green-800"
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-1">
                        {(item.fop_projects || []).map((projectId) => {
                          const project = getProjectById(projectId);
                          return (
                            <div
                              key={projectId}
                              className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-md text-xs"
                            >
                              {project?.name || 'Unknown'}
                            </div>
                          );
                        })}
                        {(!item.fop_projects || item.fop_projects.length === 0) && (
                          <span className="text-gray-400 text-xs">No projects</span>
                        )}
                      </div>
                    )}
                  </TableCell>

                  <TableCell>
                    <div className="flex gap-2">
                      {editingId === item.id ? (
                        <>
                          <Button
                            size="sm"
                            onClick={() => saveEdit(item.id)}
                            className="h-8 w-8 p-0"
                            disabled={isUpdating}
                          >
                            {isUpdating ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                            ) : (
                              <Save className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelEdit}
                            className="h-8 w-8 p-0"
                            disabled={isUpdating}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => startEdit(item)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="bg-white">
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Performance Entry</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete this performance entry? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteEntry(item.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            </Table>
          </div>
        </div>

        {filteredData.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No performance data found.
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceHistoryView;
