
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Sparkles, Loader2, Download, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useDashboard } from "@/contexts/DashboardContext";
import { parseExportRequestWithAI, AIExportRequest } from "@/services/geminiService";
import {
  ExportOptions,
  exportAllProjectsSummary,
  exportProjectWiseDetailed,
  exportAgentWise,
  exportCurrentView,
} from "@/utils/exportUtils";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface AISmartExportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AISmartExportModal: React.FC<AISmartExportModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { toast } = useToast();
  const {
    projects,
    agents,
    performanceData,
  } = useDashboard();

  const [userRequest, setUserRequest] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState("");

  const handleClose = () => {
    setUserRequest("");
    setError("");
    onClose();
  };

  const handleExport = async () => {
    if (!userRequest.trim()) {
      setError("Please enter your export request");
      return;
    }

    setIsProcessing(true);
    setError("");

    try {
      // Prepare available agents and projects for AI
      const availableAgents = agents.map(agent => agent.name);
      const availableProjects = projects
        .filter(project => project.id !== "all")
        .map(project => project.name);

      console.log("Available agents for AI:", availableAgents);
      console.log("Available projects for AI:", availableProjects);

      // Parse the request with schema-aware AI
      const aiRequest = await parseExportRequestWithAI(
        userRequest,
        availableAgents,
        availableProjects
      );

      console.log("Schema-aware AI parsed request:", aiRequest);

      // Validate that requested entities exist
      if (aiRequest.agentName && !availableAgents.includes(aiRequest.agentName)) {
        throw new Error(`Agent "${aiRequest.agentName}" not found. Available agents: ${availableAgents.slice(0, 5).join(", ")}${availableAgents.length > 5 ? "..." : ""}`);
      }

      if (aiRequest.projectName && !availableProjects.includes(aiRequest.projectName)) {
        throw new Error(`Project "${aiRequest.projectName}" not found. Available projects: ${availableProjects.slice(0, 5).join(", ")}${availableProjects.length > 5 ? "..." : ""}`);
      }

      // Apply schema-aware filtering to performance data
      let filteredData = applySchemaAwareFiltering(performanceData, aiRequest, projects, agents);

      console.log("Filtered data count:", filteredData.length);
      console.log("Applied filters:", aiRequest.filters);

      if (filteredData.length === 0) {
        throw new Error("No data found matching your criteria. Please check the agent names, project names, or date ranges and try again.");
      }

      // Convert AI request to export options
      const exportOptions = convertAIRequestToExportOptions(aiRequest, filteredData);

      setIsProcessing(false);
      setIsExporting(true);

      // Show processing toast
      toast({
        title: "AI Export Started",
        description: "Processing your schema-aware request and preparing the export...",
      });

      // Add a small delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 500));

      // Execute the export with filtered data
      await executeSchemaAwareExport(aiRequest, exportOptions, filteredData);

      // Success toast
      toast({
        title: "Export Complete!",
        description: `Successfully exported ${filteredData.length} records matching your criteria.`,
      });

      handleClose();

    } catch (error) {
      console.error("Error processing schema-aware AI export:", error);
      setError(error instanceof Error ? error.message : "Failed to process your request. Please try again.");
    } finally {
      setIsProcessing(false);
      setIsExporting(false);
    }
  };

  // Apply schema-aware filtering using the actual database structure
  const applySchemaAwareFiltering = (
    data: any[], 
    aiRequest: AIExportRequest, 
    projects: any[], 
    agents: any[]
  ) => {
    let filteredData = [...data];

    // Apply project filtering using schema mapping
    if (aiRequest.filters?.project_name || aiRequest.projectName) {
      const projectName = aiRequest.filters?.project_name || aiRequest.projectName;
      const project = projects.find(p => p.name === projectName);
      if (project) {
        filteredData = filteredData.filter(item => item.project_id === project.id);
        console.log(`Filtered by project "${projectName}":`, filteredData.length, "records");
      }
    }

    // Apply agent filtering using schema mapping
    if (aiRequest.filters?.agent_name || aiRequest.agentName) {
      const agentName = aiRequest.filters?.agent_name || aiRequest.agentName;
      const agent = agents.find(a => a.name === agentName);
      if (agent) {
        filteredData = filteredData.filter(item => item.agent_id === agent.id);
        console.log(`Filtered by agent "${agentName}":`, filteredData.length, "records");
      }
    }

    // Apply date range filtering
    if (aiRequest.filters?.date_range || (aiRequest.startDate && aiRequest.endDate)) {
      const dateRange = aiRequest.filters?.date_range || [aiRequest.startDate!, aiRequest.endDate!];
      const startDate = new Date(dateRange[0]);
      const endDate = new Date(dateRange[1]);
      
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.date);
        return itemDate >= startDate && itemDate <= endDate;
      });
      console.log(`Filtered by date range ${dateRange[0]} to ${dateRange[1]}:`, filteredData.length, "records");
    }

    // Apply single date filtering
    if (aiRequest.filters?.date) {
      const targetDate = aiRequest.filters.date;
      filteredData = filteredData.filter(item => item.date === targetDate);
      console.log(`Filtered by date "${targetDate}":`, filteredData.length, "records");
    }

    // Apply week filtering
    if (aiRequest.filters?.week || aiRequest.selectedWeek) {
      const week = aiRequest.filters?.week || aiRequest.selectedWeek;
      filteredData = filteredData.filter(item => item.week === week);
      console.log(`Filtered by week "${week}":`, filteredData.length, "records");
    }

    // Apply month filtering by extracting from date
    if (aiRequest.filters?.month || aiRequest.selectedMonth) {
      const month = aiRequest.filters?.month || aiRequest.selectedMonth;
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.date);
        const itemMonth = itemDate.toLocaleDateString('en-US', { month: 'long' });
        return itemMonth === month;
      });
      console.log(`Filtered by month "${month}":`, filteredData.length, "records");
    }

    return filteredData;
  };

  const convertAIRequestToExportOptions = (aiRequest: AIExportRequest, filteredData: any[]): ExportOptions => {
    // Find matching project and agent IDs
    const project = projects.find(p =>
      p.name.toLowerCase() === aiRequest.projectName?.toLowerCase()
    );
    const agent = agents.find(a =>
      a.name.toLowerCase() === aiRequest.agentName?.toLowerCase()
    );

    // Convert dates to Date objects
    let selectedDate: Date | undefined;
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (aiRequest.startDate && aiRequest.endDate) {
      startDate = new Date(aiRequest.startDate);
      endDate = new Date(aiRequest.endDate);
    } else if (aiRequest.startDate) {
      selectedDate = new Date(aiRequest.startDate);
    }

    return {
      scope: aiRequest.scope === "agent" ? "current-view" :
             aiRequest.scope === "project" ? "current-view" : "all-projects",
      timeFrame: aiRequest.timeFrame,
      selectedDate,
      startDate,
      endDate,
      selectedWeek: aiRequest.selectedWeek as any,
      selectedMonth: aiRequest.selectedMonth,
      selectedProject: project?.id || "All",
      selectedAgent: agent?.id,
    };
  };

  const executeSchemaAwareExport = async (
    aiRequest: AIExportRequest, 
    exportOptions: ExportOptions, 
    filteredData: any[]
  ) => {
    // Execute appropriate export function with pre-filtered data
    if (aiRequest.scope === "agent" && aiRequest.agentName) {
      // Single agent export
      exportCurrentView(projects, agents, filteredData, exportOptions);
    } else if (aiRequest.scope === "project" && aiRequest.projectName) {
      // Single project export
      const project = projects.find(p => 
        p.name.toLowerCase() === aiRequest.projectName?.toLowerCase()
      );
      if (project) {
        exportProjectWiseDetailed(
          [project],
          agents.filter(a => a.projectId === project.id),
          filteredData,
          exportOptions
        );
      }
    } else {
      // All projects export
      exportAllProjectsSummary(projects, filteredData, exportOptions);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            Schema-Aware AI Export
          </DialogTitle>
          <DialogDescription>
            Tell me what data you want in plain English. I understand your database schema and will export exactly what you need.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="request" className="text-sm font-medium">
              What data would you like to export?
            </Label>
            <Input
              id="request"
              value={userRequest}
              onChange={(e) => setUserRequest(e.target.value)}
              placeholder="e.g., HungerBox all agent June performance"
              className="mt-2"
              disabled={isProcessing || isExporting}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="text-sm text-blue-800">
              <div className="font-medium mb-2">Schema-Aware Examples:</div>
              <div className="space-y-1 text-xs">
                <div>• "HungerBox all agent June performance"</div>
                <div>• "Vandita Tiwari data from Dale Carnegie 5 to 6 June"</div>
                <div>• "DTSS week 2 performance"</div>
                <div>• "Rajesh Choudhari May data"</div>
                <div>• "export all projects this month"</div>
                <div>• "show me Opex project week 1"</div>
              </div>
              <div className="mt-2 text-xs text-blue-600 font-medium">
                💡 I understand your database schema and will map your request to the correct tables and columns!
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isProcessing || isExporting}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={!userRequest.trim() || isProcessing || isExporting}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : isExporting ? (
              <>
                <Download className="mr-2 h-4 w-4" />
                Exporting...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Export with AI
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AISmartExportModal;
