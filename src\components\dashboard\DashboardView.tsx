
import React from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import StatCard from "./StatCard";
import { Phone, Check, Clock, Calendar, CheckCircle } from "lucide-react";
import { formatTime } from "@/lib/formatters";
import PerformanceCharts from "./PerformanceCharts";
import RealTimeTopPerformers from "./RealTimeTopPerformers";

const DashboardView = () => {
  const { selectedProject, getProjectById, getTotalMetrics } = useDashboard();
  
  const project = selectedProject !== "All" 
    ? getProjectById(selectedProject) 
    : { name: "Dashboard Overview", id: "all" };
  
  const metrics = getTotalMetrics();
  
  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
          Dashboard Overview {selectedProject !== "All" && `- ${project?.name}`}
        </h1>
        <p className="text-gray-600 text-lg">Your outbound calls summary at a glance, with AI-driven insights and trends.</p>
      </div>
      
      {/* Enhanced KPI Cards with better spacing */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        <StatCard
          title="Total Dials"
          value={metrics.totalDials.toLocaleString()}
          icon={<Phone className="text-purple-500" />}
          color="bg-purple-100"
          change={{ value: 11.6, isPositive: true }}
        />
        <StatCard
          title="Total Connected"
          value={metrics.totalConnected.toLocaleString()}
          icon={<Check className="text-green-500" />}
          color="bg-green-100"
          change={{ value: 18.9, isPositive: true }}
        />
        <StatCard
          title="Total Talk Time"
          value={formatTime(metrics.totalTalkTime)}
          icon={<Clock className="text-amber-500" />}
          color="bg-amber-100"
          change={{ value: 98.3, isPositive: true }}
        />
        <StatCard
          title="Scheduled Meetings"
          value={metrics.scheduledMeetings}
          icon={<Calendar className="text-purple-500" />}
          color="bg-purple-100"
          change={{ value: 76.6, isPositive: true }}
        />
        <StatCard
          title="Successful Meetings"
          value={metrics.successfulMeetings}
          icon={<CheckCircle className="text-green-500" />}
          color="bg-green-100"
          change={{ value: 58.0, isPositive: true }}
        />
      </div>

      {/* Performance Charts Section with enhanced spacing */}
      <div className="mb-8 space-y-6">
        <PerformanceCharts />
      </div>

      {/* Real-time Top Performers Section */}
      <div className="mb-6">
        <RealTimeTopPerformers />
      </div>
    </div>
  );
};

export default DashboardView;
