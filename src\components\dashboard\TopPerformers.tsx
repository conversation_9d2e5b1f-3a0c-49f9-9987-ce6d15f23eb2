

import { useDashboard } from "@/contexts/DashboardContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, TrendingUp, Trophy, Medal, Award } from "lucide-react";

const TopPerformers = () => {
  const { agents, metrics, getProjectById, getAgentInitials, timeFrame } = useDashboard();

  // Filter metrics based on timeFrame
  const getFilteredMetrics = () => {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    
    if (timeFrame === "Daily") {
      return metrics.filter(metric => metric.date === today);
    } else if (timeFrame === "Weekly") {
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      
      return metrics.filter(metric => {
        const metricDate = new Date(metric.date);
        return metricDate >= weekStart && metricDate <= weekEnd;
      });
    } else if (timeFrame === "Monthly") {
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      return metrics.filter(metric => {
        const metricDate = new Date(metric.date);
        return metricDate >= monthStart && metricDate <= monthEnd;
      });
    }
    
    return metrics; // Return all metrics if no specific timeframe
  };

  // Enhanced performance calculation with intelligent ranking
  const agentPerformance = agents.map(agent => {
    const filteredMetrics = getFilteredMetrics();
    const agentMetrics = filteredMetrics.filter(metric => metric.agentId === agent.id);
    const totalDials = agentMetrics.reduce((sum, m) => sum + m.dials, 0);
    const totalScheduled = agentMetrics.reduce((sum, m) => sum + m.scheduledMeetings, 0);
    const totalSuccessful = agentMetrics.reduce((sum, m) => sum + m.successfulMeetings, 0);

    // Accurate success rate calculation: (Successful ÷ Scheduled) × 100
    const successRate = totalScheduled > 0 ? (totalSuccessful / totalScheduled) * 100 : 0;

    const project = getProjectById(agent.projectId);

    return {
      ...agent,
      project: project?.name || 'Unknown',
      projectCode: project?.code || 'N/A',
      projectColor: project?.color || 'bg-gray-500',
      totalDials,
      totalScheduled,
      totalMeetings: totalSuccessful,
      successRate,
      initials: getAgentInitials(agent.name)
    };
  });

  // UPDATED: Sort by dials first, then scheduled, then successful meetings
  const topPerformers = agentPerformance
    .filter(agent => agent.totalDials > 0) // Only include agents with dials
    .sort((a, b) => {
      // Primary: Total Dials (most important - activity level)
      if (a.totalDials !== b.totalDials) {
        return b.totalDials - a.totalDials;
      }
      // Secondary: Total Scheduled Meetings (if same dials count)
      if (a.totalScheduled !== b.totalScheduled) {
        return b.totalScheduled - a.totalScheduled;
      }
      // Tertiary: Total Successful Meetings (final tiebreaker)
      return b.totalMeetings - a.totalMeetings;
    })
    .slice(0, 5);

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 1:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 2:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-500">#{index + 1}</span>;
    }
  };

  const getRankBadge = (index: number) => {
    switch (index) {
      case 0:
        return <Badge className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white text-xs">⭐ Star Performer</Badge>;
      case 1:
        return <Badge className="bg-gradient-to-r from-gray-400 to-gray-600 text-white text-xs">🥈 Runner Up</Badge>;
      case 2:
        return <Badge className="bg-gradient-to-r from-amber-500 to-amber-700 text-white text-xs">🥉 Top 3</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Top 5</Badge>;
    }
  };

  const getPerformanceZone = (successRate: number) => {
    if (successRate >= 80) return { zone: 'Excellent', color: 'text-green-600', bg: 'bg-green-50' };
    if (successRate >= 60) return { zone: 'Good', color: 'text-blue-600', bg: 'bg-blue-50' };
    if (successRate >= 40) return { zone: 'Average', color: 'text-yellow-600', bg: 'bg-yellow-50' };
    return { zone: 'Needs Improvement', color: 'text-red-600', bg: 'bg-red-50' };
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-lg">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Star className="h-5 w-5 text-yellow-500" />
          <CardTitle className="text-xl">Top Performers</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {topPerformers.map((performer, index) => {
          const performanceZone = getPerformanceZone(performer.successRate);

          return (
            <div
              key={performer.id}
              className={`p-4 rounded-lg border transition-all duration-200 hover:scale-[1.02] hover:shadow-md cursor-pointer ${
                index === 0
                  ? 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-200 shadow-sm'
                  : `${performanceZone.bg} border-gray-200 hover:bg-opacity-80`
              }`}
              onClick={() => {
                alert(`${performer.name} Performance Details:\n\nRank: #${index + 1}\nTotal Dials: ${performer.totalDials}\nScheduled Meetings: ${performer.totalScheduled}\nSuccessful Meetings: ${performer.totalMeetings}\nSuccess Rate: ${performer.successRate.toFixed(1)}% (${performer.totalMeetings}/${performer.totalScheduled})\nProject: ${performer.project}\nPerformance Zone: ${performanceZone.zone}\nTime Filter: ${timeFrame}\n\nRanking: Sorted by Dials → Scheduled → Successful`);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getRankIcon(index)}
                    <div className={`w-12 h-12 rounded-full ${performer.projectColor} flex items-center justify-center text-white font-semibold text-sm transition-transform duration-200 hover:scale-110`}>
                      {performer.initials}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-gray-900">{performer.name}</span>
                      {getRankBadge(index)}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>{performer.project}</span>
                      <div className={`px-2 py-1 rounded-full text-xs ${performanceZone.bg} ${performanceZone.color}`}>
                        {performanceZone.zone}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4 text-right">
                  <div className="transition-transform duration-200 hover:scale-105">
                    <div className="text-xs text-gray-500">Dials</div>
                    <div className="font-bold text-2xl text-purple-600">
                      {performer.totalDials.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-400">calls</div>
                  </div>

                  <div className="transition-transform duration-200 hover:scale-105">
                    <div className="text-xs text-gray-500">Scheduled</div>
                    <div className="font-bold text-lg text-blue-600">
                      {performer.totalScheduled}
                    </div>
                    <div className="text-xs text-gray-400">meetings</div>
                  </div>

                  <div className="transition-transform duration-200 hover:scale-105">
                    <div className="text-xs text-gray-500">Successful</div>
                    <div className="font-semibold text-green-600">{performer.totalMeetings}</div>
                    <div className="text-xs text-gray-400">
                      {performer.successRate.toFixed(1)}% rate
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {topPerformers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p className="font-medium">No performance data available for {timeFrame.toLowerCase()} filter.</p>
            <p className="text-sm">Add some performance data to see top performers!</p>
          </div>
        )}

        <div className="mt-4 text-center text-xs text-gray-500">
          Click on performers for detailed metrics • Rankings: Dials → Scheduled → Successful • Filter: {timeFrame}
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerformers;
