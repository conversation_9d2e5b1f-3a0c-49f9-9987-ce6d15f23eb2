import React, { useState } from "react";
import { Download, ChevronDown, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { useDashboard } from "@/contexts/DashboardContext";
import {
  ExportOptions,
  ExportScope,
  exportAllProjectsSummary,
  exportProjectWiseDetailed,
  exportAgentWise,
  exportCurrentView,
  exportSpecificProjectDetail,
} from "@/utils/exportUtils";
import ProjectSelectionModal from "./ProjectSelectionModal";
import AISmartExportModal from "./AISmartExportModal";

interface ExportPerformanceButtonProps {
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost";
  size?: "default" | "sm" | "lg";
  selectedAgent?: string | null;
}

export const ExportPerformanceButton: React.FC<ExportPerformanceButtonProps> = ({
  className = "",
  variant = "outline",
  size = "default",
  selectedAgent: propSelectedAgent
}) => {
  const { toast } = useToast();
  const {
    projects,
    agents,
    performanceData,
    timeFrame,
    selectedDate,
    selectedWeek,
    selectedMonth,
    selectedProject
  } = useDashboard();

  // Use prop selectedAgent if provided, otherwise undefined
  const selectedAgent = propSelectedAgent;

  const [isExporting, setIsExporting] = useState(false);
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);

  const handleExport = async (scope: ExportScope, specificProjectId?: string) => {
    setIsExporting(true);
    
    // Show start toast
    toast({
      title: "Export Started",
      description: "Preparing your performance data export...",
    });

    try {
      const exportOptions: ExportOptions = {
        scope,
        timeFrame,
        selectedDate,
        selectedWeek,
        selectedMonth,
        selectedProject,
        selectedAgent,
        specificProjectId
      };

      // Add a small delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 500));

      switch (scope) {
        case "all-projects":
          exportAllProjectsSummary(projects, performanceData, exportOptions);
          break;
        case "project-wise":
          exportProjectWiseDetailed(projects, agents, performanceData, exportOptions);
          break;
        case "agent-wise":
          exportAgentWise(projects, agents, performanceData, exportOptions);
          break;
        case "current-view":
          exportCurrentView(projects, agents, performanceData, exportOptions);
          break;
        case "specific-project":
          exportSpecificProjectDetail(projects, agents, performanceData, exportOptions);
          break;
        default:
          throw new Error("Invalid export scope");
      }

      // Show success toast
      toast({
        title: "Export Complete!",
        description: "Your performance data has been downloaded successfully.",
        variant: "default",
      });

    } catch (error) {
      console.error("Export failed:", error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting your data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleSpecificProjectExport = (projectId: string) => {
    handleExport("specific-project", projectId);
  };

  const getExportDescription = (scope: ExportScope): string => {
    switch (scope) {
      case "all-projects":
        return "Summary data for all projects";
      case "project-wise":
        return "Detailed data grouped by project";
      case "agent-wise":
        return "Data grouped by agent across projects";
      case "current-view":
        if (selectedAgent) {
          return "Current agent's detailed performance";
        } else if (selectedProject && selectedProject !== "All") {
          return "Current project's detailed data";
        } else {
          return "Current view data";
        }
      case "specific-project":
        return "Select and export data for a specific project";
      default:
        return "";
    }
  };

  const isDataAvailable = performanceData && performanceData.length > 0;

  if (!isDataAvailable) {
    return (
      <Button
        variant={variant}
        size={size}
        className={`${className} opacity-50 cursor-not-allowed`}
        disabled
        title="No data available for export"
      >
        <Download className="h-4 w-4 mr-2" />
        Export Performance
      </Button>
    );
  }

  return (
    <>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          disabled={isExporting}
          title="Download performance data as CSV"
        >
          <Download className="h-4 w-4 mr-2" />
          {isExporting ? "Exporting..." : "Export Performance"}
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Export Options</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => setShowAIModal(true)}
          disabled={isExporting}
          className="flex flex-col items-start py-3 bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100"
        >
          <div className="font-medium flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-purple-600" />
            AI Smart Export
          </div>
          <div className="text-sm text-gray-500">
            Ask AI to export data in natural language
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => handleExport("current-view")}
          disabled={isExporting}
          className="flex flex-col items-start py-3"
        >
          <div className="font-medium">Current View</div>
          <div className="text-sm text-gray-500">
            {getExportDescription("current-view")}
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => handleExport("all-projects")}
          disabled={isExporting}
          className="flex flex-col items-start py-3"
        >
          <div className="font-medium">Project Summary</div>
          <div className="text-sm text-gray-500">
            {getExportDescription("all-projects")}
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleExport("project-wise")}
          disabled={isExporting}
          className="flex flex-col items-start py-3"
        >
          <div className="font-medium">Project Wise (Detail)</div>
          <div className="text-sm text-gray-500">
            {getExportDescription("project-wise")}
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => handleExport("agent-wise")}
          disabled={isExporting}
          className="flex flex-col items-start py-3"
        >
          <div className="font-medium">Agent Wise</div>
          <div className="text-sm text-gray-500">
            {getExportDescription("agent-wise")}
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem
          onClick={() => setShowProjectModal(true)}
          disabled={isExporting}
          className="flex flex-col items-start py-3"
        >
          <div className="font-medium">Specific Project Detail</div>
          <div className="text-sm text-gray-500">
            {getExportDescription("specific-project")}
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <ProjectSelectionModal
      isOpen={showProjectModal}
      onClose={() => setShowProjectModal(false)}
      onConfirm={handleSpecificProjectExport}
      projects={projects}
    />

    <AISmartExportModal
      isOpen={showAIModal}
      onClose={() => setShowAIModal(false)}
    />
    </>
  );
};

export default ExportPerformanceButton;
