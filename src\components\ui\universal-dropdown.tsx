import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChevronDown, Search, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface DropdownOption {
  label: string;
  value: string;
}

interface UniversalDropdownProps {
  options: DropdownOption[];
  placeholder: string;
  value?: DropdownOption | null;
  onChange: (option: DropdownOption) => void;
  className?: string;
  disabled?: boolean;
  searchable?: boolean;
  maxHeight?: string;
  minWidth?: string;
}

const UniversalDropdown: React.FC<UniversalDropdownProps> = ({
  options,
  placeholder,
  value,
  onChange,
  className = '',
  disabled = false,
  searchable = false, // Changed default to false
  maxHeight = '240px',
  minWidth = '200px' // Added default minimum width
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (option: DropdownOption) => {
    onChange(option);
    setIsOpen(false);
    setSearchTerm('');
    setHighlightedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < filteredOptions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          handleSelect(filteredOptions[highlightedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
        break;
      default:
        break;
    }
  };

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setIsOpen(false);
      setSearchTerm('');
      setHighlightedIndex(-1);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen, searchable]);

  // Reset highlighted index when search term changes
  useEffect(() => {
    setHighlightedIndex(-1);
  }, [searchTerm]);

  return (
    <div
      ref={dropdownRef}
      className={cn("relative inline-block text-left w-full", className)}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role="combobox"
      aria-expanded={isOpen}
      aria-haspopup="listbox"
      aria-owns="dropdown-menu"
      aria-label={placeholder}
    >
      {/* Dropdown Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          "w-full flex items-center justify-between px-4 py-2.5 rounded-lg shadow-sm text-sm font-medium transition-all duration-200",
          "bg-white text-gray-700 border border-gray-200 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          disabled && "opacity-50 cursor-not-allowed bg-gray-50",
          isOpen && "ring-2 ring-blue-500 border-blue-500"
        )}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-controls="dropdown-menu"
      >
        <span className={cn(
          "text-left flex-1 overflow-hidden",
          !value && "text-gray-500"
        )}>
          {value?.label || placeholder}
        </span>
        <ChevronDown
          className={cn(
            "ml-2 h-4 w-4 transition-transform duration-200 text-gray-400",
            isOpen && "transform rotate-180"
          )}
          aria-hidden="true"
        />
      </button>

      {/* Dropdown Menu with Animation */}
      {isOpen && (
        <div
          id="dropdown-menu"
          role="listbox"
          className={cn(
            "absolute left-0 mt-2 rounded-lg shadow-lg z-[9999] border border-gray-200",
            "bg-white text-gray-700 transform transition-all duration-200 ease-out",
            "animate-in fade-in-0 zoom-in-95 slide-in-from-top-2"
          )}
          style={{
            maxHeight: maxHeight,
            minWidth: minWidth,
            width: 'max-content',
            maxWidth: '400px', // Prevent extremely wide dropdowns
            overflowY: 'auto'
          }}
        >
          {/* Search Input */}
          {searchable && (
            <div className="p-3 border-b border-gray-100">
              <div className="flex items-center px-3 py-2 rounded-md bg-gray-50 border border-gray-200 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
                <Search className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-transparent outline-none text-sm placeholder-gray-500"
                  aria-label="Search options"
                />
              </div>
            </div>
          )}

          {/* Options List */}
          <div className="py-1">
            {filteredOptions.length === 0 ? (
              <div className="px-4 py-3 text-sm italic text-gray-400 text-center">
                No results found
              </div>
            ) : (
              filteredOptions.map((option, index) => (
                <button
                  key={option.value}
                  onClick={() => handleSelect(option)}
                  onMouseEnter={() => setHighlightedIndex(index)}
                  className={cn(
                    "w-full px-4 py-2.5 text-sm text-left cursor-pointer flex items-center justify-between transition-colors duration-150",
                    "hover:bg-blue-50 hover:text-blue-700",
                    highlightedIndex === index && "bg-blue-50 text-blue-700",
                    value?.value === option.value && "bg-blue-50 text-blue-700 font-semibold"
                  )}
                  role="option"
                  aria-selected={value?.value === option.value}
                >
                  <span className="whitespace-nowrap overflow-hidden text-ellipsis pr-2">{option.label}</span>
                  {value?.value === option.value && (
                    <Check className="h-4 w-4 text-blue-600 flex-shrink-0 ml-2" />
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UniversalDropdown;
