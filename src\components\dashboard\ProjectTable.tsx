
import React, { useState } from "react";
import { useDashboard } from "@/contexts/DashboardContext";
import { ArrowUp, ArrowDown } from "lucide-react";
import { formatTime } from "@/lib/formatters";
import { format, isWithinInterval, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addWeeks } from "date-fns";
import ExportPerformanceButton from "@/components/export/ExportPerformanceButton";

type SortField = "project" | "dials" | "connected" | "talkTime" | "scheduled" | "successful" | "successRate";
type SortDirection = "asc" | "desc";

const ProjectTable = () => {
  const {
    projects,
    performanceData,
    timeFrame,
    selectedDate,
    selectedWeek,
    selectedMonth,
    formatTalkTime
  } = useDashboard();
  const [sortField, setSortField] = useState<SortField>("project");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");

  // Filter out the "All" project
  const filteredProjects = projects.filter(project => project.id !== "all");

  // Helper function to get week date range from WeekFrame
  const getWeekDateRange = (weekFrame: string) => {
    // Get the current month start
    const monthStart = startOfMonth(new Date());
    // Extract week number from "Week 1", "Week 2", etc.
    const weekNumber = parseInt(weekFrame.replace('Week ', '')) - 1;
    // Calculate the start of the specific week
    const weekStart = addWeeks(startOfWeek(monthStart), weekNumber);
    const weekEnd = endOfWeek(weekStart);
    
    return { weekStart, weekEnd };
  };

  // Filter performance data based on selected timeframe
  const getFilteredPerformanceData = () => {
    return performanceData.filter(item => {
      const itemDate = new Date(item.date);

      switch (timeFrame) {
        case "Daily":
          // For daily view, show data for the selected date
          return isWithinInterval(itemDate, {
            start: startOfDay(selectedDate),
            end: endOfDay(selectedDate)
          });

        case "Weekly":
          // For weekly view, show data for the selected week
          if (selectedWeek) {
            const { weekStart, weekEnd } = getWeekDateRange(selectedWeek);
            return isWithinInterval(itemDate, {
              start: startOfDay(weekStart),
              end: endOfDay(weekEnd)
            });
          }
          // If no week selected, show current week
          return isWithinInterval(itemDate, {
            start: startOfWeek(new Date()),
            end: endOfWeek(new Date())
          });

        case "Monthly":
          // For monthly view, show data for the selected month
          if (selectedMonth && selectedMonth !== "All Time") {
            // selectedMonth is in format "May", "Jun", etc.
            const itemMonthName = format(itemDate, 'MMM');
            return itemMonthName === selectedMonth;
          }
          // If "All Time" selected, show all data
          return true;

        default:
          return true;
      }
    });
  };

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Get aggregated metrics for each project using filtered performance data
  const filteredData = getFilteredPerformanceData();

  const projectMetrics = filteredProjects.map(project => {
    const projectData = filteredData.filter(item => item.project_id === project.id);

    const dials = projectData.reduce((sum, item) => sum + item.dials, 0);
    const connected = projectData.reduce((sum, item) => sum + item.connected, 0);
    const talkTime = projectData.reduce((sum, item) => sum + item.talk_time, 0); // in seconds
    const scheduled = projectData.reduce((sum, item) => sum + item.scheduled_meetings, 0);
    const successful = projectData.reduce((sum, item) => sum + item.successful_meetings, 0);
    const successRate = scheduled > 0 ? (successful / scheduled) * 100 : 0;

    return {
      project,
      dials,
      connected,
      talkTime,
      scheduled,
      successful,
      successRate
    };
  });

  // Sort the metrics
  const sortedMetrics = [...projectMetrics].sort((a, b) => {
    if (sortField === "project") {
      return sortDirection === "asc" 
        ? a.project.name.localeCompare(b.project.name)
        : b.project.name.localeCompare(a.project.name);
    }
    
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    return sortDirection === "asc" 
      ? (aValue as number) - (bValue as number)
      : (bValue as number) - (aValue as number);
  });

  // Column header component
  const SortableHeader = ({ field, label }: { field: SortField, label: string }) => (
    <th 
      className="px-4 py-3 text-left text-sm font-medium text-gray-600 cursor-pointer"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-1">
        {label}
        {sortField === field && (
          sortDirection === "asc" ? 
            <ArrowUp size={14} /> : 
            <ArrowDown size={14} />
        )}
      </div>
    </th>
  );

  // Get current filter description
  const getFilterDescription = () => {
    switch (timeFrame) {
      case "Daily":
        return `Daily view - ${format(selectedDate, "MMM dd, yyyy")}`;
      case "Weekly":
        return selectedWeek ? `Weekly view - ${selectedWeek}` : "Weekly view";
      case "Monthly":
        return selectedMonth === "All Time" ? "All Time view" : `Monthly view - ${selectedMonth} 2025`;
      default:
        return "All data";
    }
  };

  return (
    <div className="space-y-4">
      {/* Filter Status Header */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-blue-800">
              Project Performance Overview
            </span>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-md">
              {getFilterDescription()} • {filteredData.length} entries • Real-time updates
            </span>
            <ExportPerformanceButton size="sm" />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-100 shadow-sm overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <SortableHeader field="project" label="PROJECT" />
            <SortableHeader field="dials" label="DIALS" />
            <SortableHeader field="connected" label="CONNECTED" />
            <SortableHeader field="talkTime" label="TALK TIME" />
            <SortableHeader field="scheduled" label="SCHEDULED" />
            <SortableHeader field="successful" label="SUCCESSFUL" />
            <SortableHeader field="successRate" label="SUCCESS RATE" />
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-100 bg-white">
          {sortedMetrics.map(({ project, dials, connected, talkTime, scheduled, successful, successRate }) => (
            <tr key={project.id}>
              <td className="px-4 py-3.5">
                <div className="flex items-center gap-3">
                  <div className={`avatar ${project.color}`}>{project.code}</div>
                  <span className="font-medium">{project.name}</span>
                </div>
              </td>
              <td className="px-4 py-3.5">{dials.toLocaleString()}</td>
              <td className="px-4 py-3.5">{connected.toLocaleString()}</td>
              <td className="px-4 py-3.5">{formatTalkTime(talkTime)}</td>
              <td className="px-4 py-3.5">{scheduled}</td>
              <td className="px-4 py-3.5">{successful}</td>
              <td className="px-4 py-3.5">
                <div className="flex items-center gap-2">
                  <span className={`${
                    successRate >= 60 ? 'text-green-600' : 
                    successRate >= 40 ? 'text-amber-500' : 
                    'text-red-500'
                  }`}>
                    {successRate.toFixed(1)}%
                  </span>
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        successRate >= 60 ? 'bg-green-500' : 
                        successRate >= 40 ? 'bg-amber-500' : 
                        'bg-red-500'
                      }`}
                      style={{ width: `${Math.min(100, successRate)}%` }}
                    ></div>
                  </div>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      </div>
    </div>
  );
};

export default ProjectTable;
