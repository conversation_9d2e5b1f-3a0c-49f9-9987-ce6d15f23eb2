
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useFOPProjects } from '@/hooks/dashboard/useFOPProjects';
import { useDashboard } from '@/contexts/DashboardContext';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface FOPMeetingsFormProps {
  selectedAgent: string;
  selectedDate: Date;
  onDataAdded: () => void;
}

interface FOPProjectData {
  project_id: string;
  fop_scheduled: number;
  fop_successful: number;
}

const FOPMeetingsForm: React.FC<FOPMeetingsFormProps> = ({
  selectedAgent,
  selectedDate,
  onDataAdded
}) => {
  const { agents } = useDashboard();
  const { fopProjects, isLoadingFOPProjects } = useFOPProjects();
  
  const [fopScheduled, setFopScheduled] = useState('');
  const [fopSuccessful, setFopSuccessful] = useState('');
  const [selectedFOPProject, setSelectedFOPProject] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const selectedAgentData = agents.find(agent => agent.id === selectedAgent);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedAgent || !selectedAgentData) {
      toast({
        title: "Error",
        description: "Please select an agent",
        variant: "destructive"
      });
      return;
    }

    if (!selectedFOPProject) {
      toast({
        title: "Error",
        description: "Please select a FOP project",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const fopScheduledNum = parseInt(fopScheduled) || 0;
      const fopSuccessfulNum = parseInt(fopSuccessful) || 0;

      // Check if record exists for this agent and date
      const { data: existingRecord, error: checkError } = await supabase
        .from('agent_performance')
        .select('*')
        .eq('agent_id', selectedAgent)
        .eq('date', selectedDate.toISOString().split('T')[0])
        .limit(1);

      if (checkError) throw checkError;

      if (existingRecord && existingRecord.length > 0) {
        // Update existing record
        const existing = existingRecord[0];
        let updatedFOPProjects: FOPProjectData[] = [];
        
        // Parse existing fop_projects if it exists
        if (existing.fop_projects && Array.isArray(existing.fop_projects)) {
          try {
            updatedFOPProjects = existing.fop_projects.map((item: string) => {
              if (typeof item === 'string') {
                return JSON.parse(item);
              }
              return item;
            });
          } catch (e) {
            console.error('Error parsing existing fop_projects:', e);
            updatedFOPProjects = [];
          }
        }
        
        // Add or update the FOP project data
        const projectIndex = updatedFOPProjects.findIndex((p: FOPProjectData) => p.project_id === selectedFOPProject);
        const projectData: FOPProjectData = {
          project_id: selectedFOPProject,
          fop_scheduled: fopScheduledNum,
          fop_successful: fopSuccessfulNum
        };

        if (projectIndex >= 0) {
          // Update existing project data
          updatedFOPProjects[projectIndex] = {
            ...updatedFOPProjects[projectIndex],
            fop_scheduled: (updatedFOPProjects[projectIndex].fop_scheduled || 0) + fopScheduledNum,
            fop_successful: (updatedFOPProjects[projectIndex].fop_successful || 0) + fopSuccessfulNum
          };
        } else {
          // Add new project data
          updatedFOPProjects.push(projectData);
        }

        // Convert to array of JSON strings for storage
        const fopProjectsArray = updatedFOPProjects.map(project => JSON.stringify(project));

        const { error: updateError } = await supabase
          .from('agent_performance')
          .update({
            fop_scheduled: existing.fop_scheduled + fopScheduledNum,
            fop_successful: existing.fop_successful + fopSuccessfulNum,
            fop_projects: fopProjectsArray,
            updated_at: new Date().toISOString()
          })
          .eq('id', existing.id);

        if (updateError) throw updateError;
      } else {
        // Create new record
        const fopProjectsData: FOPProjectData[] = [{
          project_id: selectedFOPProject,
          fop_scheduled: fopScheduledNum,
          fop_successful: fopSuccessfulNum
        }];

        // Convert to array of JSON strings for storage
        const fopProjectsArray = fopProjectsData.map(project => JSON.stringify(project));

        const { error: insertError } = await supabase
          .from('agent_performance')
          .insert({
            agent_id: selectedAgent,
            project_id: selectedAgentData.projectId,
            date: selectedDate.toISOString().split('T')[0],
            dials: 0,
            connected: 0,
            talk_time: 0,
            scheduled_meetings: 0,
            successful_meetings: 0,
            fop_scheduled: fopScheduledNum,
            fop_successful: fopSuccessfulNum,
            fop_projects: fopProjectsArray,
            success_rate: 0,
            sync_source: 'manual'
          });

        if (insertError) throw insertError;
      }

      toast({
        title: "Success",
        description: "FOP meeting data added successfully",
      });

      // Reset form
      setFopScheduled('');
      setFopSuccessful('');
      setSelectedFOPProject('');
      onDataAdded();

    } catch (error) {
      console.error('Error adding FOP data:', error);
      toast({
        title: "Error",
        description: "Failed to add FOP meeting data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="fop-project">FOP Project</Label>
          <Select value={selectedFOPProject} onValueChange={setSelectedFOPProject} disabled={isLoadingFOPProjects}>
            <SelectTrigger>
              <SelectValue placeholder={isLoadingFOPProjects ? "Loading projects..." : "Select FOP project"} />
            </SelectTrigger>
            <SelectContent>
              {fopProjects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${project.color}`}></div>
                    <span>{project.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="fop-scheduled">FOP Scheduled</Label>
          <Input
            id="fop-scheduled"
            type="number"
            min="0"
            value={fopScheduled}
            onChange={(e) => setFopScheduled(e.target.value)}
            placeholder="Enter FOP scheduled count"
          />
        </div>
        
        <div>
          <Label htmlFor="fop-successful">FOP Successful</Label>
          <Input
            id="fop-successful"
            type="number"
            min="0"
            value={fopSuccessful}
            onChange={(e) => setFopSuccessful(e.target.value)}
            placeholder="Enter FOP successful count"
          />
        </div>
      </div>

      <Button 
        type="submit" 
        disabled={isSubmitting || !selectedAgent || !selectedFOPProject}
        className="w-full"
      >
        {isSubmitting ? "Adding..." : "Add FOP Meeting Data"}
      </Button>
    </form>
  );
};

export default FOPMeetingsForm;
