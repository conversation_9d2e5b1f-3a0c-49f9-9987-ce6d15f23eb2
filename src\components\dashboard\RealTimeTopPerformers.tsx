
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Trophy, Medal, Award, Loader2, TrendingUp, Phone, Calendar, CheckCircle } from "lucide-react";
import { useRealTimePerformers, RealTimePerformer } from "@/hooks/dashboard/useRealTimePerformers";
import { useDashboard } from "@/contexts/DashboardContext";

const RealTimeTopPerformers: React.FC = () => {
  const { performers, isLoading, error } = useRealTimePerformers();
  const { timeFrame, getAgentInitials } = useDashboard();

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-500">#{rank}</span>;
    }
  };

  const getRankBadge = (rank: number) => {
    switch (rank) {
      case 1:
        return <Badge className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white text-xs font-semibold">🥇 Star Performer</Badge>;
      case 2:
        return <Badge className="bg-gradient-to-r from-gray-400 to-gray-600 text-white text-xs font-semibold">🥈 Runner Up</Badge>;
      case 3:
        return <Badge className="bg-gradient-to-r from-amber-500 to-amber-700 text-white text-xs font-semibold">🥉 Top 3</Badge>;
      default:
        return <Badge variant="outline" className="text-xs font-semibold">Top 5</Badge>;
    }
  };

  const getProjectColor = (projectCode: string) => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-red-500', 
      'bg-yellow-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ];
    const index = projectCode.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const getFilterDescription = () => {
    switch (timeFrame) {
      case "Daily":
        return "Daily rankings";
      case "Weekly":
        return "Weekly rankings";
      case "Monthly":
        return "Monthly rankings";
      default:
        return "Real-time rankings";
    }
  };

  if (isLoading) {
    return (
      <Card className="transition-all duration-200 hover:shadow-lg">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <CardTitle className="text-xl">Top 5 Performers</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-gray-500">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading real-time performance data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="transition-all duration-200 hover:shadow-lg">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <CardTitle className="text-xl">Top 5 Performers</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="text-red-500 mb-2">⚠️ Data Fetch Error</div>
          <p className="text-gray-600 text-sm">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-200 hover:shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <CardTitle className="text-xl">Top 5 Performers</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-500">Live Data</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filter Status */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                {getFilterDescription()} • Ranked by Successful Meetings
              </span>
            </div>
            <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-md">
              {timeFrame} View • Real-time Updates
            </span>
          </div>
        </div>

        {performers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p className="font-medium">No performance data available</p>
            <p className="text-sm">No performance records found for the selected period</p>
          </div>
        ) : (
          performers.map((performer: RealTimePerformer) => {
            const initials = getAgentInitials(performer.agent_name);
            const projectColor = getProjectColor(performer.project_code);

            return (
              <div
                key={performer.agent_id}
                className={`p-4 rounded-lg border transition-all duration-200 hover:scale-[1.02] hover:shadow-md cursor-pointer ${
                  performer.rank === 1
                    ? 'bg-gradient-to-r from-yellow-50 to-amber-50 border-yellow-200 shadow-sm'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
                onClick={() => {
                  alert(`${performer.agent_name} Performance Details:\n\nRank: #${performer.rank}\nProject: ${performer.project_name} (${performer.project_code})\nSuccessful Meetings: ${performer.total_successful}\nScheduled Meetings: ${performer.total_scheduled}\nTotal Dials: ${performer.total_dials}\nFilter: ${timeFrame}\n\nRanking: Based on Successful Meetings Count`);
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      {getRankIcon(performer.rank)}
                      <div className={`w-12 h-12 rounded-full ${projectColor} flex items-center justify-center text-white font-semibold text-sm transition-transform duration-200 hover:scale-110`}>
                        {initials}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-gray-900">{performer.agent_name}</span>
                        {getRankBadge(performer.rank)}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>{performer.project_name}</span>
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {performer.project_code}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-6 text-right">
                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
                        <CheckCircle className="w-3 h-3" />
                        <span>Successful</span>
                      </div>
                      <div className="font-bold text-2xl text-green-600">
                        {performer.total_successful}
                      </div>
                      <div className="text-xs text-gray-400">meetings</div>
                    </div>

                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
                        <Calendar className="w-3 h-3" />
                        <span>Scheduled</span>
                      </div>
                      <div className="font-bold text-lg text-blue-600">
                        {performer.total_scheduled}
                      </div>
                      <div className="text-xs text-gray-400">meetings</div>
                    </div>

                    <div className="transition-transform duration-200 hover:scale-105">
                      <div className="flex items-center gap-1 text-xs text-gray-500 mb-1">
                        <Phone className="w-3 h-3" />
                        <span>Dials</span>
                      </div>
                      <div className="font-semibold text-purple-600">{performer.total_dials.toLocaleString()}</div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}

        <div className="mt-4 text-center text-xs text-gray-500">
          Click on performers for detailed metrics • Rankings: Successful Meetings → Scheduled Meetings • Filter: {timeFrame} • Real-time Supabase data
        </div>
      </CardContent>
    </Card>
  );
};

export default RealTimeTopPerformers;
