
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, CheckCircle, XCircle, RefreshCw } from "lucide-react";
import { useDashboard } from "@/contexts/DashboardContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

interface SyncStatus {
  agent_id: string;
  agent_name: string;
  project_id: string;
  project_name: string;
  last_sync: string | null;
  sync_status: 'updated' | 'not_updated';
  message?: string;
}

const RealTimeDataTracker = () => {
  const { agents, projects } = useDashboard();
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProject, setSelectedProject] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  // Fetch sync statuses from sync_logs table
  const fetchSyncStatuses = async () => {
    try {
      setIsLoading(true);
      
      // Get recent sync logs
      const { data: syncLogs, error } = await supabase
        .from('sync_logs')
        .select('*')
        .order('synced_at', { ascending: false });

      if (error) {
        console.error('Error fetching sync logs:', error);
        toast({
          title: "Error",
          description: "Failed to fetch sync status",
          variant: "destructive"
        });
        return;
      }

      // Create sync status for each agent
      const statuses: SyncStatus[] = agents.map(agent => {
        const project = projects.find(p => p.id === agent.projectId);
        const recentSync = syncLogs?.find(log => 
          log.agent_name.toLowerCase() === agent.name.toLowerCase()
        );

        return {
          agent_id: agent.id,
          agent_name: agent.name,
          project_id: agent.projectId || '',
          project_name: project?.name || 'Unknown Project',
          last_sync: recentSync?.synced_at || null,
          sync_status: recentSync?.sync_status === 'success' ? 'updated' : 'not_updated',
          message: recentSync?.message || (recentSync ? 'Data synced successfully' : 'No sync data available')
        };
      });

      setSyncStatuses(statuses);
    } catch (error) {
      console.error('Error in fetchSyncStatuses:', error);
      toast({
        title: "Error",
        description: "Failed to load sync data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSyncStatuses();
    
    // Set up real-time subscription for sync_logs
    const channel = supabase
      .channel('sync-logs-realtime')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sync_logs'
      }, () => {
        fetchSyncStatuses();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [agents, projects]);

  // Filter sync statuses based on search and project
  const filteredStatuses = syncStatuses.filter(status => {
    const matchesSearch = status.agent_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         status.project_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesProject = selectedProject === "all" || status.project_id === selectedProject;
    
    return matchesSearch && matchesProject;
  });

  const updatedAgents = filteredStatuses.filter(s => s.sync_status === 'updated');
  const notUpdatedAgents = filteredStatuses.filter(s => s.sync_status === 'not_updated');

  const StatusCard = ({ status }: { status: SyncStatus }) => (
    <Card className="mb-3">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium">{status.agent_name}</span>
              <Badge variant="secondary">{status.project_name}</Badge>
            </div>
            <p className="text-sm text-gray-600">{status.message}</p>
            {status.last_sync && (
              <p className="text-xs text-gray-500 mt-1">
                Last sync: {new Date(status.last_sync).toLocaleString()}
              </p>
            )}
          </div>
          <div className="ml-4">
            {status.sync_status === 'updated' ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-red-500" />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Loading sync data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Track Real-Time Data</h2>
          <p className="text-gray-600">Monitor data sync status from Runo webhook</p>
        </div>
        <Button onClick={fetchSyncStatuses} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search agents or projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedProject} onValueChange={setSelectedProject}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select project" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Projects</SelectItem>
            {projects.map(project => (
              <SelectItem key={project.id} value={project.id}>
                {project.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Tabs for Updated/Not Updated */}
      <Tabs defaultValue="updated" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="updated" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Data Updated ({updatedAgents.length})
          </TabsTrigger>
          <TabsTrigger value="not-updated" className="flex items-center gap-2">
            <XCircle className="h-4 w-4" />
            Data Not Updated ({notUpdatedAgents.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="updated" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-green-600">Successfully Updated Agents</CardTitle>
              <CardDescription>
                Agents whose data was successfully synced from Runo
              </CardDescription>
            </CardHeader>
            <CardContent>
              {updatedAgents.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No agents have been successfully updated yet
                </p>
              ) : (
                <div className="space-y-2">
                  {updatedAgents.map(status => (
                    <StatusCard key={status.agent_id} status={status} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="not-updated" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Not Updated Agents</CardTitle>
              <CardDescription>
                Agents whose data could not be synced (name mismatch or sync issues)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {notUpdatedAgents.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  All agents are successfully synced
                </p>
              ) : (
                <div className="space-y-2">
                  {notUpdatedAgents.map(status => (
                    <StatusCard key={status.agent_id} status={status} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RealTimeDataTracker;
